Please implement the GitHub issue: $ARGUMENTS.

Follow these steps:

1. Use `gh issue view $ARGUMENTS` to get the issue details
1. Think about the problem and search the codebase for relevant files
1. Create a feature branch: `git checkout -b feature/issue-$ARGUMENTS`
1. Write tests first when possible (TDD approach)
1. Implement the necessary changes to fix the issue
1. Run tests and ensure code passes linting: `pnpm test && pnpm lint`
1. Generate frontend types if backend API changed: `cd frontend && pnpm schema:generate`
1. Update CHANGELOG.md under [Unreleased] section
1. Commit with conventional format: `git commit -m "feat: implement issue #$ARGUMENTS"`
1. Push and create a PR: `gh pr create --title "feat: implement issue #$ARGUMENTS" --body "Closes #$ARGUMENTS"`

Remember to use the GitHub CLI (`gh`) for all GitHub-related tasks and follow the project's SQLModel, TypeScript, and testing conventions.
